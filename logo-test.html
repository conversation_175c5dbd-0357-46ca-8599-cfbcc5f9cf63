<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo响应式测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e4e7ed;
        }

        .test-section {
            margin-bottom: 40px;
        }

        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #303133;
        }

        .viewport-simulator {
            border: 2px solid #409eff;
            border-radius: 8px;
            margin: 20px 0;
            overflow: hidden;
            position: relative;
        }

        .viewport-label {
            position: absolute;
            top: -2px;
            left: -2px;
            background: #409eff;
            color: white;
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 0 0 8px 0;
        }

        .login-mockup {
            background: white;
            padding: 40px 32px 24px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }

        /* 复制登录页面的Logo样式 - 更新后的尺寸 */
        .company-logo {
            height: auto;
            width: 100%;
            max-width: min(200px, 40vw);
            min-width: 80px;
            object-fit: contain;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .system-title {
            font-size: 24px;
            font-weight: 600;
            margin: 16px 0 6px;
            color: #303133;
            line-height: 1.3;
        }

        .system-subtitle {
            font-size: 13px;
            color: #909399;
            margin: 0;
            font-weight: 400;
        }

        /* 不同视窗尺寸模拟 */
        .desktop-large {
            width: 100%;
            max-width: 1920px;
        }

        .desktop-standard {
            width: 100%;
            max-width: 1366px;
        }

        .tablet-landscape {
            width: 100%;
            max-width: 1024px;
        }

        .tablet-portrait {
            width: 100%;
            max-width: 768px;
        }

        .mobile-large {
            width: 100%;
            max-width: 414px;
        }

        .mobile-standard {
            width: 100%;
            max-width: 375px;
        }

        .mobile-small {
            width: 100%;
            max-width: 320px;
        }

        /* 媒体查询测试 */
        @media (max-width: 768px) {
            .company-logo {
                max-width: min(130px, 38vw);
                min-width: 70px;
            }
        }

        @media (max-width: 480px) {
            .company-logo {
                max-width: min(120px, 40vw);
                min-width: 60px;
            }
        }

        .info-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-size: 14px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .resize-instructions {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .resize-instructions h3 {
            color: #1890ff;
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <div class="test-header">
            <h1>Logo响应式测试页面</h1>
            <p>测试登录页面Logo在不同分辨率下的显示效果</p>
        </div>

        <div class="resize-instructions">
            <h3>📐 测试说明</h3>
            <p>1. 拖动浏览器窗口改变大小，观察Logo的响应式变化</p>
            <p>2. 使用浏览器开发者工具的设备模拟器测试不同分辨率</p>
            <p>3. 检查Logo是否保持宽高比，无拉伸变形</p>
            <p>4. 验证在小屏幕下Logo仍然清晰可见</p>
        </div>

        <div class="test-section">
            <h2 class="test-title">🖥️ 桌面端测试 (1920px+)</h2>
            <div class="viewport-simulator desktop-large">
                <div class="viewport-label">桌面大屏 1920px+</div>
                <div class="login-mockup">
                    <img src="/logo.png" alt="宿迁烟草" class="company-logo" />
                    <h1 class="system-title">合同审核系统</h1>
                    <p class="system-subtitle">Contract Review System</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">📱 平板端测试 (768px-1024px)</h2>
            <div class="viewport-simulator tablet-landscape">
                <div class="viewport-label">平板横屏 1024px</div>
                <div class="login-mockup">
                    <img src="/logo.png" alt="宿迁烟草" class="company-logo" />
                    <h1 class="system-title">合同审核系统</h1>
                    <p class="system-subtitle">Contract Review System</p>
                </div>
            </div>

            <div class="viewport-simulator tablet-portrait">
                <div class="viewport-label">平板竖屏 768px</div>
                <div class="login-mockup">
                    <img src="/logo.png" alt="宿迁烟草" class="company-logo" />
                    <h1 class="system-title">合同审核系统</h1>
                    <p class="system-subtitle">Contract Review System</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">📱 移动端测试 (320px-480px)</h2>
            <div class="viewport-simulator mobile-large">
                <div class="viewport-label">大屏手机 414px</div>
                <div class="login-mockup">
                    <img src="/logo.png" alt="宿迁烟草" class="company-logo" />
                    <h1 class="system-title">合同审核系统</h1>
                    <p class="system-subtitle">Contract Review System</p>
                </div>
            </div>

            <div class="viewport-simulator mobile-standard">
                <div class="viewport-label">标准手机 375px</div>
                <div class="login-mockup">
                    <img src="/logo.png" alt="宿迁烟草" class="company-logo" />
                    <h1 class="system-title">合同审核系统</h1>
                    <p class="system-subtitle">Contract Review System</p>
                </div>
            </div>

            <div class="viewport-simulator mobile-small">
                <div class="viewport-label">小屏手机 320px</div>
                <div class="login-mockup">
                    <img src="/logo.png" alt="宿迁烟草" class="company-logo" />
                    <h1 class="system-title">合同审核系统</h1>
                    <p class="system-subtitle">Contract Review System</p>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <h3>🎯 测试要点</h3>
            <div class="info-row">
                <span><strong>宽高比一致性:</strong></span>
                <span>Logo应保持原始宽高比，不拉伸变形</span>
            </div>
            <div class="info-row">
                <span><strong>最小尺寸限制:</strong></span>
                <span>最小宽度60-80px，确保可读性</span>
            </div>
            <div class="info-row">
                <span><strong>最大尺寸限制:</strong></span>
                <span>使用vw单位，随视窗宽度自适应</span>
            </div>
            <div class="info-row">
                <span><strong>平滑过渡:</strong></span>
                <span>尺寸变化应有0.3s过渡动画</span>
            </div>
        </div>
    </div>

    <script>
        // 实时显示Logo尺寸信息
        function updateLogoInfo() {
            const logos = document.querySelectorAll('.company-logo');
            logos.forEach((logo, index) => {
                const rect = logo.getBoundingClientRect();
                const computedStyle = window.getComputedStyle(logo);

                console.log(`Logo ${index + 1}:`, {
                    width: Math.round(rect.width),
                    height: Math.round(rect.height),
                    aspectRatio: (rect.width / rect.height).toFixed(2),
                    maxWidth: computedStyle.maxWidth,
                    minWidth: computedStyle.minWidth
                });
            });
        }

        // 窗口大小改变时更新信息
        window.addEventListener('resize', updateLogoInfo);
        window.addEventListener('load', updateLogoInfo);
    </script>
</body>

</html>